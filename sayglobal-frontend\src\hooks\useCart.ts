import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { cartService } from '@/services/api';
import { userService } from '@/services/api';

// Cart item tipi
export interface CartItem {
    variantId: number;
    productId: number;
    quantity: number;
    price: number;
    stock: number;
    stockStatus: number;
    pv: number;
    cv: number;
    sp: number;
    productName: string;
    brandName: string;
    mainImageUrl: string;
}

// Cart response tipi
export interface CartResponse {
    isCustomerPrice: boolean;
    items: CartItem[];
}

// Sepet içeriklerini getir
export const useCartItems = () => {
    return useQuery<CartResponse>({
        queryKey: ['cartItems'],
        queryFn: async () => {
            const response = await cartService.getCartItems();
            if (response.success) {
                return response.data.data; // API response'u data wrapper'ı içinde geliyor
            }
            throw new Error(response.error || 'Sepet içerikleri alınamadı');
        },
        staleTime: 30 * 1000, // 30 saniye boyunca veriyi taze kabul et
        refetchOnWindowFocus: true, // Sayfa odaklandığında yenile
        refetchOnMount: true, // Component mount olduğunda yenile
    });
};

// İndirim oranını getir
export const useDiscountRate = () => {
    return useQuery<{ discountRate: number }>({
        queryKey: ['discountRate'],
        queryFn: async () => {
            try {
                const response = await userService.getDiscountRate();
                if (response.success) {
                    return response.data.data || { discountRate: 0 }; // API response'u data wrapper'ı içinde geliyor
                }
                // Hata durumunda 0 döndür, throw etme
                console.warn('İndirim oranı alınamadı:', response.error);
                return { discountRate: 0 };
            } catch (error) {
                // Network hatası vs. durumunda da 0 döndür
                console.warn('İndirim oranı alınırken hata:', error);
                return { discountRate: 0 };
            }
        },
        staleTime: 5 * 60 * 1000, // 5 dakika boyunca veriyi taze kabul et
        refetchOnWindowFocus: false, // Sayfa odaklandığında yenileme
        refetchOnMount: true, // Component mount olduğunda yenile
        retry: false, // Hata durumunda tekrar deneme
    });
};

// Sepetten ürün çıkarma mutation'ı
export const useRemoveFromCart = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (productVariantId: number) => {
            const response = await cartService.removeFromCart(productVariantId);
            if (!response.success) {
                throw new Error(response.error || 'Ürün sepetten çıkarılamadı');
            }
            return response.data;
        },
        onSuccess: () => {
            // Sepet verilerini yenile
            queryClient.invalidateQueries({ queryKey: ['cartItems'] });
        },
        onError: (error) => {
            console.error('Sepetten ürün çıkarma hatası:', error);
        },
    });
};

// Sepet ürün miktarını güncelleme mutation'ı
export const useUpdateCartQuantity = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async ({ productVariantId, quantity }: { productVariantId: number; quantity: number }) => {
            const response = await cartService.updateCartQuantity(productVariantId, quantity);
            if (!response.success) {
                throw new Error(response.error || 'Ürün miktarı güncellenemedi');
            }
            return response.data;
        },
        onSuccess: () => {
            // Sepet verilerini yenile
            queryClient.invalidateQueries({ queryKey: ['cartItems'] });
        },
        onError: (error) => {
            console.error('Sepet ürün miktarı güncelleme hatası:', error);
        },
    });
};

// Puan hesaplama fonksiyonu
export const calculatePoints = (ratio: number, price: number): number => {
    return Math.round((ratio / 100) * price);
};

// Fiyat hesaplama fonksiyonu (indirim dahil)
export const calculateDiscountedPrice = (originalPrice: number, discountRate: number | null, isCustomerPrice: boolean): number => {
    if (isCustomerPrice || !discountRate || discountRate <= 0) {
        return originalPrice;
    }
    return originalPrice * (1 - discountRate / 100);
};
